<?php

class TravelPlanValidationRules
{
    public function forSet($set)
    {
        if ($set === 'lite') {
            return $this->lite();
        }

        return $this->default();
    }

    private function lite()
    {
        return array_merge($this->base(), [
            'other_info' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please provide some details about your trip',
                ],
            ]
        ]);
    }

    private function default()
    {
        return [
            'first_name' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please enter your first name',
                ],
            ],
            'last_name' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please enter your last name',
                ],
            ],
            'email_address' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please enter your email address',
                ],
                'email' => [
                    'rule' => 'email',
                    'message' => 'Please enter a valid email address',
                ],
            ],
            'telephone_number' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please enter your telephone number',
                ],
            ],
            'num_adults' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please select the number of adults',
                ],
            ],
            'num_children' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please select the number of children',
                ],
            ],
            'travel_date' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please select a travel date',
                ],
            ],
            'destination_country' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please select a destination country',
                ],
            ],
            'destination_region' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please select a destination region',
                ],
            ],
            'min_budget' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please select a minimum budget',
                ],
            ],
            'max_budget' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please select a maximum budget',
                ],
            ],
            'other_info' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please provide some details about your trip',
                ],
            ],
            'follow_up_method' => [
                'notEmpty' => [
                    'rule' => ['validateFollowUpMethod'],
                    'message' => 'Please select at least one follow-up method',
                ],
                'validateContactDetails' => [
                    'rule' => ['validateContactDetails'],
                    'message' => 'Please provide contact details for the selected follow-up methods',
                ],
            ],
        ];
    }

    private function base()
    {
        return [
            'first_name' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please enter your first name',
                ],
            ],
            'last_name' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please enter your last name',
                ],
            ],
            'email_address' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please provide an email address',
                ],
                'validEmail' => [
                    'rule' => 'validEmail',
                    'message' => 'Please provide a valid email address',
                ]
            ],
            'telephone_number' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please provide a telephone number',
                ],
            ],
            'follow_up_method' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please select at least one follow-up method',
                ],
                'validateContactDetails' => [
                    'rule' => ['validateContactDetails'],
                    'message' => 'Please provide contact details for the selected follow-up methods',
                ],
            ],
        ];
    }

    public function validateContactDetails($check) {
        $debugLogging = Configure::read('BVContactWebService.debugLogging') ?? false;

        if ($debugLogging) {
            CakeLog::write('debug', '=== Validating Contact Details ===');
            CakeLog::write('debug', 'Input data: ' . json_encode($check));
        }

        $followUpMethods = $check['follow_up_method'];
        $emailAddress = $this->data['TravelPlan']['email_address'];
        $telephoneNumber = $this->data['TravelPlan']['telephone_number'];

        if ($debugLogging) {
            CakeLog::write('debug', 'Follow up methods: ' . json_encode($followUpMethods));
            CakeLog::write('debug', 'Email address: ' . $emailAddress);
            CakeLog::write('debug', 'Telephone number: ' . $telephoneNumber);
        }

        if (in_array('Email', $followUpMethods) && empty($emailAddress)) {
            if ($debugLogging) {
                CakeLog::write('debug', 'Email validation failed - email address required');
            }
            return false;
        }
        if (in_array('Phone', $followUpMethods) && empty($telephoneNumber)) {
            if ($debugLogging) {
                CakeLog::write('debug', 'Phone validation failed - telephone number required');
            }
            return false;
        }
        if (in_array('Text', $followUpMethods) && empty($telephoneNumber)) {
            if ($debugLogging) {
                CakeLog::write('debug', 'Text validation failed - telephone number required');
            }
            return false;
        }

        if ($debugLogging) {
            CakeLog::write('debug', 'Contact details validation PASSED');
        }
        return true;
    }

    public function validateFollowUpMethod($check) {
        $debugLogging = Configure::read('BVContactWebService.debugLogging') ?? false;

        if ($debugLogging) {
            CakeLog::write('debug', '=== Validating Follow Up Method ===');
            CakeLog::write('debug', 'Input data: ' . json_encode($check));
        }

        $value = is_array($check['follow_up_method']) ? $check['follow_up_method'] : [];

        if ($debugLogging) {
            CakeLog::write('debug', 'Processed value: ' . json_encode($value));
        }

        $result = !empty($value);

        if ($debugLogging) {
            CakeLog::write('debug', 'Validation result: ' . ($result ? 'PASSED' : 'FAILED'));
        }
        return $result;
    }
}


