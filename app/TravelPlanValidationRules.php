<?php

class TravelPlanValidationRules
{
    public function forSet($set)
    {
        if ($set === 'lite') {
            return $this->lite();
        }

        return $this->default();
    }

    private function lite()
    {
        return array_merge($this->base(), [
            'other_info' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please provide some details about your trip',
                ],
            ]
        ]);
    }

    private function default()
    {
        return [
            'first_name' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please enter your first name',
                ],
            ],
            'last_name' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please enter your last name',
                ],
            ],
            'email_address' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please enter your email address',
                ],
                'email' => [
                    'rule' => 'email',
                    'message' => 'Please enter a valid email address',
                ],
            ],
            'telephone_number' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please enter your telephone number',
                ],
            ],
            'num_adults' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please select the number of adults',
                ],
            ],
            'num_children' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please select the number of children',
                ],
            ],
            'travel_date' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please select a travel date',
                ],
            ],
            'destination_country' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please select a destination country',
                ],
            ],
            'destination_region' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please select a destination region',
                ],
            ],
            'min_budget' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please select a minimum budget',
                ],
            ],
            'max_budget' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please select a maximum budget',
                ],
            ],
            'other_info' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please provide some details about your trip',
                ],
            ],
            'follow_up_method' => [
                'notEmpty' => [
                    'rule' => ['validateFollowUpMethod'],
                    'message' => 'Please select at least one follow-up method',
                ],
                'validateContactDetails' => [
                    'rule' => ['validateContactDetails'],
                    'message' => 'Please provide contact details for the selected follow-up methods',
                ],
            ],
        ];
    }

    private function base()
    {
        return [
            'first_name' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please enter your first name',
                ],
            ],
            'last_name' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please enter your last name',
                ],
            ],
            'email_address' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please provide an email address',
                ],
                'validEmail' => [
                    'rule' => 'validEmail',
                    'message' => 'Please provide a valid email address',
                ]
            ],
            'telephone_number' => [
                'notEmpty' => [
                    'rule' => 'notEmpty',
                    'message' => 'Please provide a telephone number',
                ],
            ],
            'follow_up_method' => [
                'validateFollowUpMethod' => [
                    'rule' => ['validateFollowUpMethod'],
                    'message' => 'Please select at least one follow-up method',
                ],
                'validateContactDetails' => [
                    'rule' => ['validateContactDetails'],
                    'message' => 'Please provide contact details for the selected follow-up methods',
                ],
            ],
        ];
    }

}


