<?php

require(APP.'Recaptcha.php');
require(APP.'ContactService.php');
require(APP.'EmailList.php');

App::import('Sanitize');


class TravelPlansController extends AppController {

    var $name = 'TravelPlans';
    var $components = array('Email', 'Auth', 'Security', 'Navigation');

    public $criticalCss = 'travelPlan';

    function beforeFilter() {
        parent::beforeFilter();

        if ($this->Auth->allowedActions <> array('*')) {
            $this->Auth->allowedActions = array_merge($this->Auth->allowedActions, array('add', 'add_lite','add_social'));
        }
        $this->Auth->allow('add_social');

        /** Hide the non-CakePHP generated fields from the security checker */
        $this->Security->disabledFields = ['TravelPlan.token', 'TravelPlan.contact_method', 'TravelPlan.num_adults', 'TravelPlan.num_children', 'TravelPlan.min_budget', 'TravelPlan.max_budget', 'TravelPlan.destination_region', 'TravelPlan.destination_country','TravelPlan.source', 'TravelPlan.utm'];

        $this->setupEmail();
    }

    function setupEmail() {
        if ($this->isLocal()) {
            $this->Email->smtpOptions = [
                'port' => env('MAIL_PORT'),
                'timeout' => '30',
                'host' => env('MAIL_HOST'),
                'username' => env('MAIL_USERNAME'),
                'password' => env('MAIL_PASSWORD'),
            ];
        } else {
            $this->Email->smtpOptions = [
                'port' => Configure::read('Email.smtp.port'),
                'timeout' => '30',
                'host' => Configure::read('Email.smtp.hostname'),
                'username' => Configure::read('Email.smtp.username'),
                'password' => Configure::read('Email.smtp.password'),
            ];
        }

        /* Set delivery method */
        $this->Email->delivery = 'smtp';

        $this->Email->to = Configure::read('Email.travel_plan.to');
        $this->Email->subject = Configure::read('Email.travel_plan.subject');
        $this->Email->replyTo = Configure::read('Email.travel_plan.replyTo');
        $this->Email->from = Configure::read('Email.travel_plan.from');
    }

    function add() {
        $this->set([
            'hideBreadcrumb' => true,
            'hideHero' => true,
            'hideContactUsBanner' => true,
            'navigation' => $this->TravelPlan->navigation,
            'years' => $this->TravelPlan->years(),
            'budgets' => $this->TravelPlan->budgets(),
            'validationErrors' => [],
        ]);

        $this->_setEnumLists();

        if (empty($this->data)) {
            return;
        }

        if (!$this->isLocal()) {
        $recaptchaErrors = (new Recaptcha)->errors($this->data['TravelPlan']['token'], 'contact_form');

        if ($recaptchaErrors) {
            $this->Session->setFlash('There is a problem with some of the detail entered: ' . $recaptchaErrors[0]);
            return;
        }
        }

        // Remove captcha fields
        unset($this->data['TravelPlan']['token']);
        unset($this->data['TravelPlan']['action']);

        if ($this->data['TravelPlan']['travel_date_month'] === 'Flexible') {
            $this->data['TravelPlan']['travel_date'] = 'Flexible ' . $this->data['TravelPlan']['travel_date_year'];
        } else {
            $this->data['TravelPlan']['travel_date'] = $this->data['TravelPlan']['travel_date_month'] . ' ' . $this->data['TravelPlan']['travel_date_year'];
        }

        $validationErrors = $this->storeTravelPlanInDatabase();

        if ($validationErrors) {
            $this->set([
                'validationErrors' => $validationErrors,
            ]);

            return;
        }

        $this->Session->setFlash('', 'site_flash_good', ['hide_form' => true]); // We will contact you shortly to discuss your quote

        $sendToSalesPortal = $this->sendToSalesPortal();

        if (!$sendToSalesPortal) {
            $this->sendAdminEmail(Configure::read('Email.travel_plan.template'));
        }

        $signUpToNewsletter = filter_var($this->data['TravelPlan']['email_consent'], FILTER_VALIDATE_BOOLEAN);

        if ($signUpToNewsletter) {
            $success = $this->signUpToNewsletter();

            if (!$success) {
                return;
            }
        }

        $this->redirect([]);
    }

    function add_social() {
        error_log('add_social action started');

        $this->set([
            'hideBreadcrumb' => true,
            'hideHero' => true,
            'hideContactUsBanner' => true,
            'navigation' => $this->TravelPlan->navigation,
            'years' => $this->TravelPlan->years(),
            'budgets' => $this->TravelPlan->budgets(),
            'validationErrors' => [],
        ]);

        $this->_setEnumLists();

        if (empty($this->data)) {
            error_log('No form data submitted');
            return;
        }

        error_log('Form data received');

        if (!$this->isLocal()) {
            error_log('Checking recaptcha');
            $recaptchaErrors = (new Recaptcha)->errors($this->data['TravelPlan']['token'], 'contact_form');

            if ($recaptchaErrors) {
                error_log('Recaptcha errors: ' . json_encode($recaptchaErrors));
                $this->Session->setFlash('There is a problem with some of the detail entered: ' . $recaptchaErrors[0]);
                return;
            }
        }

        // Remove captcha fields
        unset($this->data['TravelPlan']['token']);
        unset($this->data['TravelPlan']['action']);

        error_log('Processing travel date');
        if ($this->data['TravelPlan']['travel_date_month'] === 'Flexible') {
            $this->data['TravelPlan']['travel_date'] = 'Flexible ' . $this->data['TravelPlan']['travel_date_year'];
        } else {
            $this->data['TravelPlan']['travel_date'] = $this->data['TravelPlan']['travel_date_month'] . ' ' . $this->data['TravelPlan']['travel_date_year'];
        }

        error_log('Calling storeTravelPlanInDatabase');
        $validationErrors = $this->storeTravelPlanInDatabase();
        error_log('Validation errors: ' . json_encode($validationErrors));

        CakeLog::write('log', 'validationErrors: ' . json_encode($validationErrors));

        if (!empty($validationErrors)) {
            $this->set('validationErrors', $validationErrors);
            return;
        }

        error_log('Form submission successful');
        $this->Session->setFlash('', 'site_flash_good', ['hide_form' => true]); // We will contact you shortly to discuss your quote

        $sendToSalesPortal = $this->sendToSalesPortal();

        CakeLog::write('log','SEND: '.$sendToSalesPortal);

        if (!$sendToSalesPortal) {
            error_log('Sending admin email');
            $this->sendAdminEmail(Configure::read('Email.travel_plan.template'));
        }

        $signUpToNewsletter = filter_var($this->data['TravelPlan']['email_consent'], FILTER_VALIDATE_BOOLEAN);

        if ($signUpToNewsletter) {
            error_log('Attempting newsletter signup');
            $success = $this->signUpToNewsletter();
            error_log('Newsletter signup result: ' . ($success ? 'success' : 'failed'));

            if (!$success) {
                return;
            }
        }

        $this->redirect([]);
    }

    function add_lite() {
        try {
            $this->set([
                'hideBreadcrumb' => true,
                'hideHero' => true,
                'hideContactUsBanner' => true,
                'navigation' => $this->TravelPlan->navigation,
                'years' => $this->TravelPlan->years(),
                'budgets' => $this->TravelPlan->budgets(),
                'validationErrors' => [],
                'contactMethods' => $this->TravelPlan->enums['contact_methods'],
                'destinationCountries' => $this->TravelPlan->enums['destination_countries'],
                'destinationRegions' => $this->TravelPlan->enums['destination_regions'],
                'months' => $this->TravelPlan->enums['months'],
            ]);

            $prepopulateData = [];

            if (!empty($this->params['url']['message'])) {
                $prepopulateData['message'] = Sanitize::paranoid($this->params['url']['message'],[' ', '-','%','+',':']);
            }
            $this->set('prepopulateData', $prepopulateData);

            $this->_setEnumLists();

            if (empty($this->data)) {
                return;
            }

            // Log form submission start
            $this->logToFile('Form submission started - ' . json_encode([
                'email' => isset($this->data['TravelPlan']['email_address']) ? $this->data['TravelPlan']['email_address'] : 'not set',
                'name' => isset($this->data['TravelPlan']['name']) ? $this->data['TravelPlan']['name'] : 'not set',
                'ip' => $_SERVER['REMOTE_ADDR']
            ]));

            if (!$this->isLocal()) {
                $recaptchaErrors = (new Recaptcha)->errors($this->data['TravelPlan']['token'], 'contact_form');

                if ($recaptchaErrors) {
                    $this->logToFile('Recaptcha validation failed: ' . json_encode($recaptchaErrors), 'error');
                    $this->Session->setFlash('There is a problem with some of the detail entered: ' . $recaptchaErrors[0]);
                    return;
                }
            }

            // Remove captcha fields
            unset($this->data['TravelPlan']['token']);
            unset($this->data['TravelPlan']['action']);

            if ($this->data['TravelPlan']['travel_date_month'] === 'Flexible') {
                $this->data['TravelPlan']['travel_date'] = 'Flexible ' . $this->data['TravelPlan']['travel_date_year'];
            } else {
                $this->data['TravelPlan']['travel_date'] = $this->data['TravelPlan']['travel_date_month'] . ' ' . $this->data['TravelPlan']['travel_date_year'];
            }

            // Set a default source if not provided
            if (!isset($this->data['TravelPlan']['source'])) {
                $this->data['TravelPlan']['source'] = 'make_an_enquiry';
            }

            // Set empty title for lite form since we don't collect it
            $this->data['TravelPlan']['title'] = '';

            // Set default follow_up_method if not provided
            if (!isset($this->data['TravelPlan']['follow_up_method']) ||
                !is_array($this->data['TravelPlan']['follow_up_method']) ||
                empty($this->data['TravelPlan']['follow_up_method'])) {
                $this->data['TravelPlan']['follow_up_method'] = ['Phone', 'Email', 'Text'];
            }

            $this->TravelPlan->setValidationRules('lite');

            $validationErrors = $this->storeTravelPlanInDatabase();

            if ($validationErrors) {
                $this->logToFile('Validation errors: ' . json_encode($validationErrors), 'error');
                $this->set([
                    'validationErrors' => $validationErrors,
                ]);

                return;
            }

            $this->logToFile('Form validation passed, travel plan saved to database');
            $this->Session->setFlash('', 'site_flash_good', ['hide_form' => true]); // We will contact you shortly to discuss your quote

            $sendToSalesPortal = $this->sendToSalesPortal();
            $this->logToFile('Send to sales portal result: ' . ($sendToSalesPortal ? 'success' : 'failed'));

            if (!$sendToSalesPortal) {
                $this->logToFile('Sending admin email');
                $this->sendAdminEmail(Configure::read('Email.travel_plan_lite.template'));
            }

            $signUpToNewsletter = filter_var($this->data['TravelPlan']['email_consent'], FILTER_VALIDATE_BOOLEAN);

            if ($signUpToNewsletter) {
                $this->logToFile('Attempting newsletter signup');
                $success = $this->signUpToNewsletter();
                $this->logToFile('Newsletter signup result: ' . ($success ? 'success' : 'failed'));

                if (!$success) {
                    return;
                }
            }

            $this->logToFile('Form submission completed successfully');
            $this->redirect([]);
        } catch (Exception $e) {
            // Log the exception
            $this->logToFile('Exception in add_lite: ' . $e->getMessage() . "\nStack trace: " . $e->getTraceAsString(), 'error');

            // Set a flash message for the user
            $this->Session->setFlash('An error occurred while processing your request. Please try again or contact support.', 'site_flash_bad');

            // Set validation errors to an empty array to prevent undefined variable errors
            $this->set('validationErrors', []);
        }
    }

    private function storeTravelPlanInDatabase()
    {
        $debugLogging = Configure::read('BVContactWebService.debugLogging') ?? false;

        if ($debugLogging) {
            $this->logToFile('=== Form Submission Started ===', 'debug');
            $this->logToFile('Raw form data: ' . json_encode($this->data), 'debug');
        }

        $this->TravelPlan->set($this->data);

        if ($debugLogging) {
            $this->logToFile('Data set on model: ' . json_encode($this->TravelPlan->data), 'debug');
        }

        if (!$this->TravelPlan->validates()) {
            $validationErrors = $this->TravelPlan->invalidFields();
            if ($debugLogging) {
                $this->logToFile('Validation failed with errors: ' . json_encode($validationErrors), 'debug');
                $this->logToFile('Current model data: ' . json_encode($this->TravelPlan->data), 'debug');
            }
            $this->Session->setFlash('There is a problem with some of the detail entered. Please correct the errors below.');

            return $validationErrors;
        }

        if ($debugLogging) {
            $this->logToFile('Validation passed successfully', 'debug');
        }

        $response = $this->_validateEmail($this->data['TravelPlan']['email_address']);

        if (!$response) {
            if ($debugLogging) {
                $this->logToFile('Email validation failed', 'debug');
            }
            $this->Session->setFlash('There is a problem with some of the detail entered. Please correct the errors below.');
            $this->TravelPlan->invalidate('email_address', 'Please enter a valid email address');

            return $this->TravelPlan->invalidFields();
        }

        if ($debugLogging) {
            $this->logToFile('Email validation passed', 'debug');
        }
        $this->TravelPlan->save();
        if ($debugLogging) {
            $this->logToFile('Travel plan saved to database', 'debug');
            $this->logToFile('=== Form Submission Completed ===', 'debug');
        }

        return null;
    }

    private function sendToSalesPortal()
    {
        // Log the start of the process
        $this->logToFile('Starting sendToSalesPortal method');

        try {
            // Create a new ContactService instance
            $contactService = new ContactService();

            // Log the data being sent
            $this->logToFile('Sending data to ContactService: ' . json_encode($this->data['TravelPlan']));

            // Call the createTravelPlan method
            $url = $contactService->createTravelPlan($this->data['TravelPlan']);

            // Log the result
            $this->logToFile('Result from ContactService: ' . ($url ? $url : 'null'));

        CakeLog::write('log', 'url: ' . $url);
        CakeLog::write('log', 'data: ' . json_encode($this->data['TravelPlan']));

            if (is_null($url)) {
                $this->logToFile('ContactService returned null URL', 'error');
                return false;
            }

            // Send the email with the URL
            $this->Email->send($url);
            $this->logToFile('Email sent with URL: ' . $url);

            return true;
        } catch (Exception $e) {
            // Log any exceptions
            $this->logToFile('Exception in sendToSalesPortal: ' . $e->getMessage(), 'error');
            $this->logToFile('Stack trace: ' . $e->getTraceAsString(), 'error');
            return false;
        }
    }

    private function sendAdminEmail($template)
    {
        try {
            // Set up email configuration for travel_plan_lite
            $this->Email->to = Configure::read('Email.travel_plan_lite.to');
            $this->Email->subject = Configure::read('Email.travel_plan_lite.subject');
            $this->Email->replyTo = Configure::read('Email.travel_plan_lite.replyTo');
            $this->Email->from = Configure::read('Email.travel_plan_lite.from');
            $this->Email->template = $template;
            $this->Email->sendAs = 'both';
            $this->Email->send();
            $this->logToFile('Admin email sent successfully');
        } catch (Exception $e) {
            $this->logToFile('Failed to send admin email: ' . $e->getMessage(), 'error');
            // Don't throw the exception - just log it and continue
        }
    }

    private function isLocal()
    {
        return strpos($_SERVER['HTTP_HOST'],"localhost") !== false || strpos($_SERVER['HTTP_HOST'],"stage") !== false || strpos($_SERVER['HTTP_HOST'],"ddev") !== false;
    }

    /**
     * Custom logging function that writes to a file regardless of debug settings
     * @param string $message The message to log
     * @param string $level The log level (info, error, debug)
     * @return void
     */
    private function logToFile($message, $level = 'info')
    {
        $logDir = APP . 'tmp' . DS . 'logs';
        $logFile = $logDir . DS . 'form_submissions.log';

        // Create directory if it doesn't exist
        if (!file_exists($logDir)) {
            mkdir($logDir, 0755, true);
        }

        // Format the log message as JSON
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => $level,
            'message' => $message,
            'request_id' => isset($_SERVER['UNIQUE_ID']) ? $_SERVER['UNIQUE_ID'] : uniqid(),
            'ip' => isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'unknown'
        ];

        // Add form data if available
        if (isset($this->data) && !empty($this->data)) {
            $formData = $this->data;
            // Remove token for security
            if (isset($formData['TravelPlan']) && isset($formData['TravelPlan']['token'])) {
                unset($formData['TravelPlan']['token']);
            }
            $logData['form_data'] = $formData;
        }

        // Add session ID for tracking
        if (isset($_SESSION)) {
            $logData['session_id'] = session_id();
        }

        // Convert to JSON and append newline
        $jsonLog = json_encode($logData, JSON_PRETTY_PRINT) . PHP_EOL;

        // Write to file
        file_put_contents($logFile, $jsonLog, FILE_APPEND);
    }

    private function signUpToNewsletter()
    {
        $code = (new EmailList)->subscribe($this->data['TravelPlan']['email_address'], $this->data['TravelPlan']['first_name'], $this->data['TravelPlan']['last_name']);

        if ($code == 400) {
            $this->Session->setFlash('There is a problem with some of the detail entered. Please correct the errors below.');
            $this->TravelPlan->invalidate('email_address', 'Please enter a valid email address');

            $this->set([
                'validationErrors' => $this->TravelPlan->invalidFields(),
            ]);

            return false;
        }

        if ($code != 200 && $code != 202) {
            $this->Session->setFlash('There was a problem sending your sign up request, please try submitting the form again');
            return false;
        }

        return true;
    }
}
