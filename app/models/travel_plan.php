<?php

require(APP.'TravelPlanValidationRules.php');

class TravelPlan extends AppModel {

  var $name = 'TravelPlan';

  var $enums = array(
    'contact_methods' => [
        'Appointment' => "I'd like to make an appointment to visit your Southampton Office",
        'Consultation Call' => "I'd like a consultation call",
        'Video Call' => "I'd like a video call",
    ],
    'destination_countries' => [
        'USA',
        'Canada',
    ],
    'destination_regions' => [
        'USA' => [
            'California' => 'California',
            'The West' => 'The West',
            'Hawaii' => 'Hawaii',
            'Deep South' => 'Deep South',
            'East Coast' => 'East Coast',
            'Florida' => 'Florida',
            'Pacific Northwest' => 'Pacific Northwest',
            'Rocky Mountains' => 'Rocky Mountains',
            'The Carolinas' => 'The Carolinas',
            'Texas' => 'Texas',
            'Central States' => 'Central States',
            'Great Lakes' => 'Great Lakes',
            'Capital Region' => 'Capital Region',
            'New England' => 'New England',
            'Alaska' => 'Alaska',
            'Bahamas' => 'Bahamas',
            'Multi-City' => 'Multi-City',
            'Unsure' => 'Unsure',
            'Holiday Type: Route 66' => 'Holiday Type: Route 66',
            'Holiday Type: Cruise' => 'Holiday Type: Cruise',
            'Holiday Type: Escorted' => 'Holiday Type: Escorted',
            'Holiday Type: RV' => 'Holiday Type: RV',
            'Holiday Type: Motorcycle' => 'Holiday Type: Motorcycle',
            'Holiday Type: A mix of the above' => 'Holiday Type: A mix of the above'
        ],
        'Canada' => [
            'Alberta' => 'Alberta',
            'British Columbia' => 'British Columbia',
            'Rocky Mountraineer Train' => 'Rocky Mountraineer Train',
            'Ontario' => 'Ontario',
            'Quebec' => 'Quebec',
            'Manitoba' => 'Manitoba',
            'Newfoundland & Labrador' => 'Newfoundland & Labrador',
            'Nova Scotia' => 'Nova Scotia',
            'New Brunswick' => 'New Brunswick',
            'Prince Edward Island' => 'Prince Edward Island',
            'Yukon' => 'Yukon',
            'Multi-City' => 'Multi-City',
            'Unsure' => 'Unsure',
            'Holiday Type: Route 66' => 'Holiday Type: Route 66',
            'Holiday Type: Cruise' => 'Holiday Type: Cruise',
            'Holiday Type: Escorted' => 'Holiday Type: Escorted',
            'Holiday Type: RV' => 'Holiday Type: RV',
            'Holiday Type: Motorcycle' => 'Holiday Type: Motorcycle',
            'Holiday Type: A mix of the above' => 'Holiday Type: A mix of the above'
        ],
    ],
    'titles' => [
        'Mr',
        'Mrs',
        'Ms',
        'Miss',
        'Dr',
        'Prof',
        'Lord',
        'Lady',
        'Sir',
    ],
    'months' => [
        "January",
        "February",
        "March",
        "April",
        "May",
        "June",
        "July",
        "August",
        "September",
        "October",
        "November",
        "December",
        "Flexible"
    ],
  );

  var $navigation = array(
  );

  function __construct($id = false, $table = null, $ds = null)
  {
    parent::__construct($id, $table, $ds);

    $this->setValidationRules();
  }

  function years() {
    $currentYear = intval(date('Y'));

    $years = [];
    for ($i = $currentYear; $i <= $currentYear + 15; $i++) {
        $years[$i] = $i;
    }

    return $years;
  }

  function budgets() {
    $min = 2500;
    $max = 10000;

    $options = [];
    for ($i = $min; $i <= $max; $i += 500) {
        $formattedNumber = number_format($i, 0, '.', "'");
        $options[$i] = $i === $max ? "£{$formattedNumber}+" : "£{$formattedNumber}";
    }

    return $options;
  }

  function confirmed($check)
  {
    $confirmedValue = array_shift($check);

    $value = $this->data['TravelPlan']['email_address'];

    return $value === $confirmedValue;
  }

  function inCountry($check)
  {
    $region = array_shift($check);

    $country = $this->data['TravelPlan']['destination_country'];

    return isset($this->enums['destination_regions'][$country][$region]);
  }

  function min($check, $minValue)
  {
    $number = intval(array_shift($check));

    return $number >= $minValue;
  }

  function validEmail($check)
  {
    $email = array_shift($check);

    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
  }

  function setValidationRules($set = 'default')
  {
    $rules = new TravelPlanValidationRules();

    $this->validate = $rules->forSet($set);
  }

  function beforeValidate($options = array()) {
    $debugLogging = Configure::read('BVContactWebService.debugLogging') ?? false;

    if ($debugLogging) {
      $this->logToFile('beforeValidate called - ' . json_encode($this->data), 'debug');
    }

    if (!isset($this->data['TravelPlan']['follow_up_method']) ||
        !is_array($this->data['TravelPlan']['follow_up_method']) ||
        empty($this->data['TravelPlan']['follow_up_method'])) {
      if ($debugLogging) {
        $this->logToFile('Setting default follow_up_method values', 'debug');
      }
      $this->data['TravelPlan']['follow_up_method'] = ['Phone', 'Email', 'Text'];
    }

    if ($debugLogging) {
      $this->logToFile('beforeValidate completed - ' . json_encode($this->data), 'debug');
    }
    return true;
  }

  function validateFollowUpMethod($check) {
    $value = is_array($check['follow_up_method']) ? $check['follow_up_method'] : [];
    return !empty($value);
  }

  function validateContactDetails($check) {
    $followUpMethods = $check['follow_up_method'];
    $emailAddress = $this->data['TravelPlan']['email_address'];
    $telephoneNumber = $this->data['TravelPlan']['telephone_number'];

    if (in_array('Email', $followUpMethods) && empty($emailAddress)) {
      return false;
    }
    if (in_array('Phone', $followUpMethods) && empty($telephoneNumber)) {
      return false;
    }
    if (in_array('Text', $followUpMethods) && empty($telephoneNumber)) {
      return false;
    }

    return true;
  }
}
